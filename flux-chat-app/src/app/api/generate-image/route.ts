import { NextRequest, NextResponse } from 'next/server';
import {
  generateImage,
  generateMultipleImages,
  applyStylePreset,
  uploadImageToFal,
  ImageGenerationParams,
  STYLE_PRESETS
} from '@/lib/imageGeneration';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      prompt,
      model = 'flux-schnell',
      size = 'landscape',
      num_images = 1,
      style_preset,
      guidance_scale,
      num_inference_steps,
      seed,
      negative_prompt,
      batch_mode = false,
      // 图像编辑参数
      image_file,  // base64编码的图片数据
      image_url,   // 图片URL
      strength = 0.95  // 编辑强度
    } = body;

    // 验证必需参数
    if (!prompt || typeof prompt !== 'string') {
      return NextResponse.json(
        { error: 'Prompt is required and must be a string' },
        { status: 400 }
      );
    }

    // 处理图片上传（如果有）
    let finalImageUrl = image_url;
    if (image_file && !image_url) {
      try {
        // 将base64转换为Blob对象（Node.js兼容）
        const base64Data = image_file.split(',')[1]; // 移除data:image/...;base64,前缀
        const mimeType = image_file.split(',')[0].split(':')[1].split(';')[0];
        const buffer = Buffer.from(base64Data, 'base64');

        // 创建一个类似File的对象，兼容fal.ai的上传接口
        const fileObject = {
          stream: () => new ReadableStream({
            start(controller) {
              controller.enqueue(new Uint8Array(buffer));
              controller.close();
            }
          }),
          arrayBuffer: () => Promise.resolve(buffer.buffer),
          name: 'uploaded-image',
          type: mimeType,
          size: buffer.length
        };

        // 上传到fal.ai存储
        finalImageUrl = await uploadImageToFal(fileObject as File);
        console.log('Image uploaded to fal.ai:', finalImageUrl);
      } catch (uploadError) {
        console.error('Failed to upload image:', uploadError);
        return NextResponse.json(
          { error: 'Failed to upload image' },
          { status: 500 }
        );
      }
    }

    // 构建生成参数
    let params: ImageGenerationParams = {
      prompt,
      model,
      size,
      num_images: Math.min(num_images, 4), // 限制最多4张
      guidance_scale,
      num_inference_steps,
      seed,
      negative_prompt,
      // 图像编辑参数
      image_url: finalImageUrl,
      strength,
    };

    // 检查API密钥，如果没有则返回演示结果
    const hasValidFalKey = (process.env.FAL_KEY && process.env.FAL_KEY !== 'your_fal_api_key_here') ||
                          (process.env.FAL_API_KEY && process.env.FAL_API_KEY !== 'your_fal_api_key_here');

    if (!hasValidFalKey) {
      // 演示模式 - 返回模拟的图像生成结果
      return NextResponse.json({
        success: true,
        demo_mode: true,
        images: [
          {
            url: 'https://picsum.photos/1024/768?random=' + Date.now(),
            width: 1024,
            height: 768,
            content_type: 'image/jpeg'
          }
        ],
        metadata: {
          model_used: params.model,
          generation_time: 3000 + Math.random() * 2000, // 模拟3-5秒生成时间
          seed: Math.floor(Math.random() * 1000000),
          prompt_used: prompt,
          parameters: params,
          demo_notice: 'This is a demo image. Configure FAL_API_KEY for real image generation.'
        }
      });
    }

    // 应用风格预设
    if (style_preset && style_preset in STYLE_PRESETS) {
      params = applyStylePreset(params, style_preset as keyof typeof STYLE_PRESETS);
    }

    console.log('Image generation request:', {
      prompt: prompt.substring(0, 100) + '...',
      model,
      size,
      num_images: params.num_images,
      has_image_file: !!image_file,
      has_image_url: !!finalImageUrl,
      is_image_editing: !!finalImageUrl
    });

    // 生成图像
    const result = batch_mode && num_images > 1
      ? await generateMultipleImages(params, num_images)
      : await generateImage(params);

    if (result.success) {
      return NextResponse.json({
        success: true,
        images: result.images,
        metadata: {
          model_used: result.model_used,
          generation_time: result.generation_time,
          seed: result.seed,
          prompt_used: prompt,
          parameters: params
        }
      });
    } else {
      return NextResponse.json(
        { 
          error: result.error,
          success: false,
          model_used: result.model_used
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Image generation API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET方法用于获取支持的模型和配置
export async function GET() {
  try {
    return NextResponse.json({
      models: {
        'flux-schnell': {
          name: 'FLUX Schnell',
          description: '快速生成，4步即可完成',
          speed: 'fast',
          quality: 'good'
        },
        'flux-dev': {
          name: 'FLUX Dev',
          description: '开发版本，平衡速度和质量',
          speed: 'medium',
          quality: 'high'
        },
        'flux-pro': {
          name: 'FLUX Pro',
          description: '专业版本，最高质量',
          speed: 'slow',
          quality: 'excellent'
        },
        'sd-xl': {
          name: 'Stable Diffusion XL',
          description: '经典的高质量模型',
          speed: 'medium',
          quality: 'high'
        }
      },
      sizes: {
        'square': '1024x1024',
        'portrait': '768x1024',
        'landscape': '1024x768',
        'wide': '1344x768',
        'tall': '768x1344'
      },
      style_presets: Object.keys(STYLE_PRESETS),
      limits: {
        max_images_per_request: 4,
        max_prompt_length: 1000
      }
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to get configuration' },
      { status: 500 }
    );
  }
}
