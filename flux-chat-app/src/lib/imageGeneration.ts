import * as fal from '@fal-ai/serverless-client';

// 配置fal.ai客户端
fal.config({
  credentials: process.env.FAL_KEY || process.env.FAL_API_KEY,
});

// 支持的图像生成模型
export const IMAGE_MODELS = {
  // FLUX模型 - 高质量图像生成
  'flux-pro': 'fal-ai/flux-pro',
  'flux-dev': 'fal-ai/flux/dev',
  'flux-schnell': 'fal-ai/flux/schnell',

  // FLUX Image-to-Image模型
  'flux-dev-img2img': 'fal-ai/flux/dev/image-to-image',

  // Stable Diffusion模型
  'sd-xl': 'fal-ai/stable-diffusion-xl',
  'sd-3': 'fal-ai/stable-diffusion-v3-medium',

  // 其他专业模型
  'playground': 'fal-ai/playground-v2-5',
  'kandinsky': 'fal-ai/kandinsky-3',
} as const;

// 图像尺寸预设 - 映射到fal.ai的格式
export const IMAGE_SIZES = {
  'square': 'square_hd',
  'portrait': 'portrait_4_3',
  'landscape': 'landscape_4_3',
  'wide': 'landscape_16_9',
  'tall': 'portrait_16_9',
} as const;

// 尺寸对应的实际像素值（用于显示）
export const IMAGE_SIZE_PIXELS = {
  'square_hd': { width: 1024, height: 1024 },
  'square': { width: 512, height: 512 },
  'portrait_4_3': { width: 768, height: 1024 },
  'portrait_16_9': { width: 576, height: 1024 },
  'landscape_4_3': { width: 1024, height: 768 },
  'landscape_16_9': { width: 1024, height: 576 },
} as const;

// 图像生成参数接口
export interface ImageGenerationParams {
  prompt: string;
  model?: keyof typeof IMAGE_MODELS;
  size?: keyof typeof IMAGE_SIZES;
  num_images?: number;
  guidance_scale?: number;
  num_inference_steps?: number;
  seed?: number;
  safety_tolerance?: number;
  negative_prompt?: string;
  // 图像编辑参数
  image_url?: string;  // 用于图像编辑的输入图像URL
  strength?: number;   // 编辑强度 (0-1)
}

// 图像生成结果接口
export interface ImageGenerationResult {
  success: boolean;
  images?: Array<{
    url: string;
    width: number;
    height: number;
    content_type: string;
  }>;
  error?: string;
  model_used: string;
  generation_time?: number;
  seed?: number;
}

// 上传图片到fal.ai存储
export async function uploadImageToFal(imageFile: File): Promise<string> {
  try {
    console.log('Uploading image to fal.ai storage...');
    const url = await fal.storage.upload(imageFile);
    console.log('Image uploaded successfully:', url);
    return url;
  } catch (error) {
    console.error('Failed to upload image:', error);
    throw new Error('Failed to upload image to fal.ai storage');
  }
}

// 主要图像生成函数
export async function generateImage(params: ImageGenerationParams): Promise<ImageGenerationResult> {
  // 检查API密钥
  const hasValidFalKey = (process.env.FAL_KEY && process.env.FAL_KEY !== 'your_fal_api_key_here') ||
                        (process.env.FAL_API_KEY && process.env.FAL_API_KEY !== 'your_fal_api_key_here');

  if (!hasValidFalKey) {
    return {
      success: false,
      error: 'FAL API key not configured',
      model_used: params.model || 'flux-schnell',
    };
  }

  try {
    // 判断是否为图像编辑模式
    const isImageEditing = params.image_url && params.image_url.length > 0;

    // 如果是图像编辑，强制使用flux-dev-img2img模型
    const model = isImageEditing ? 'flux-dev-img2img' : (params.model || 'flux-schnell');
    const modelEndpoint = IMAGE_MODELS[model];
    const imageSize = IMAGE_SIZES[params.size || 'landscape'];

    console.log(`Generating image with model: ${model} (${modelEndpoint})`);
    console.log('Is image editing mode:', isImageEditing);

    // 构建请求参数 - 根据不同模型使用不同的参数格式
    let requestParams: any = {
      prompt: params.prompt,
    };

    // 图像编辑模式 (Image-to-Image)
    if (isImageEditing && model === 'flux-dev-img2img') {
      console.log('Setting up FLUX image-to-image parameters with image_url:', params.image_url);
      requestParams = {
        image_url: params.image_url,
        prompt: params.prompt,
        strength: params.strength || 0.95,
        num_inference_steps: params.num_inference_steps || 40,
        guidance_scale: params.guidance_scale || 3.5,
        num_images: params.num_images || 1,
        enable_safety_checker: true,
        ...(params.seed && { seed: params.seed }),
      };
    }
    // FLUX文本生成图像模式
    else if (model.startsWith('flux') && !isImageEditing) {
      requestParams = {
        prompt: params.prompt,
        image_size: imageSize,
        num_images: params.num_images || 1,
        num_inference_steps: getDefaultSteps(model),
        enable_safety_checker: true,
        ...(params.seed && { seed: params.seed }),
      };
    }
    // 其他模型的参数格式（SD等）
    else {
      const pixelSize = IMAGE_SIZE_PIXELS[imageSize as keyof typeof IMAGE_SIZE_PIXELS] || IMAGE_SIZE_PIXELS['landscape_4_3'];
      requestParams = {
        prompt: params.prompt,
        width: pixelSize.width,
        height: pixelSize.height,
        num_images: params.num_images || 1,
        guidance_scale: params.guidance_scale || 7.5,
        num_inference_steps: params.num_inference_steps || getDefaultSteps(model),
        ...(params.negative_prompt && { negative_prompt: params.negative_prompt }),
        ...(params.seed && { seed: params.seed }),
      };
    }

    console.log(`Generating image with model: ${model} (${modelEndpoint})`);
    console.log('Request parameters:', JSON.stringify(requestParams, null, 2));

    const startTime = Date.now();

    // 调用fal.ai API
    const result = await fal.subscribe(modelEndpoint, {
      input: requestParams,
      logs: true,
      onQueueUpdate: (update) => {
        console.log('Queue update:', update);
      },
    });

    const generationTime = Date.now() - startTime;

    // fal.ai返回的数据直接在result中，不在result.data中
    if (result && result.images && result.images.length > 0) {
      const pixelSize = model.startsWith('flux')
        ? IMAGE_SIZE_PIXELS[imageSize as keyof typeof IMAGE_SIZE_PIXELS] || IMAGE_SIZE_PIXELS['landscape_4_3']
        : IMAGE_SIZE_PIXELS[imageSize as keyof typeof IMAGE_SIZE_PIXELS] || IMAGE_SIZE_PIXELS['landscape_4_3'];

      return {
        success: true,
        images: result.images.map((img: any) => ({
          url: img.url,
          width: img.width || pixelSize.width,
          height: img.height || pixelSize.height,
          content_type: img.content_type || 'image/jpeg',
        })),
        model_used: model,
        generation_time: generationTime,
        seed: result.seed,
      };
    } else {
      throw new Error('No images generated');
    }

  } catch (error) {
    console.error('Image generation error:', error);

    // 提取更详细的错误信息
    let errorMessage = 'Unknown error occurred';
    if (error instanceof Error) {
      errorMessage = error.message;
      // 如果是fal.ai的错误，尝试提取更多信息
      if ('body' in error && error.body) {
        console.error('Error body:', error.body);
        errorMessage += ` (Status: ${(error as any).status})`;
      }
    }

    return {
      success: false,
      error: errorMessage,
      model_used: params.model || 'flux-schnell',
    };
  }
}

// 获取模型默认步数
function getDefaultSteps(model: keyof typeof IMAGE_MODELS): number {
  const stepMap = {
    'flux-pro': 50,
    'flux-dev': 50,
    'flux-schnell': 4,  // FLUX Schnell专为快速生成优化
    'flux-dev-img2img': 40,  // FLUX Dev Image-to-Image默认步数
    'sd-xl': 30,
    'sd-3': 28,
    'playground': 50,
    'kandinsky': 100,
  };
  return stepMap[model] || 30;
}

// 批量图像生成
export async function generateMultipleImages(
  params: ImageGenerationParams,
  count: number = 4
): Promise<ImageGenerationResult> {
  try {
    const batchParams = {
      ...params,
      num_images: Math.min(count, 4), // 限制单次最多4张
    };
    
    return await generateImage(batchParams);
  } catch (error) {
    console.error('Batch image generation error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Batch generation failed',
      model_used: params.model || 'flux-schnell',
    };
  }
}

// 图像风格预设
export const STYLE_PRESETS = {
  'photorealistic': {
    negative_prompt: 'cartoon, anime, painting, drawing, sketch, low quality, blurry',
    guidance_scale: 7.5,
  },
  'artistic': {
    negative_prompt: 'low quality, blurry, distorted',
    guidance_scale: 8.0,
  },
  'anime': {
    negative_prompt: 'realistic, photograph, low quality, blurry',
    guidance_scale: 7.0,
  },
  'cartoon': {
    negative_prompt: 'realistic, photograph, dark, scary, low quality',
    guidance_scale: 7.0,
  },
  'cinematic': {
    negative_prompt: 'low quality, amateur, snapshot, casual',
    guidance_scale: 8.5,
  },
} as const;

// 应用风格预设
export function applyStylePreset(
  params: ImageGenerationParams,
  style: keyof typeof STYLE_PRESETS
): ImageGenerationParams {
  const preset = STYLE_PRESETS[style];
  return {
    ...params,
    negative_prompt: preset.negative_prompt,
    guidance_scale: preset.guidance_scale,
  };
}

// 检查fal.ai服务状态
export async function checkServiceStatus(): Promise<boolean> {
  try {
    // 尝试调用一个简单的模型来检查服务状态
    await fal.subscribe('fal-ai/flux/schnell', {
      input: {
        prompt: 'test',
        image_size: 'square',
        num_images: 1,
        num_inference_steps: 1,
      },
      timeout: 5000,
    });
    return true;
  } catch (error) {
    console.error('Service status check failed:', error);
    return false;
  }
}
