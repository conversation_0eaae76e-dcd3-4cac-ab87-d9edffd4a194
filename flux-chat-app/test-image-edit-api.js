// 测试图像编辑API
const fs = require('fs');
const path = require('path');

async function testImageEditAPI() {
    try {
        console.log('Testing Image Edit API...');
        
        // 创建一个简单的测试图片数据（1x1像素的PNG）
        const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';
        
        const requestData = {
            prompt: 'convert to cartoon style',
            model: 'flux-schnell',
            size: 'landscape',
            num_images: 1,
            image_file: testImageBase64
        };
        
        console.log('Sending request with image data...');
        console.log('Request data:', {
            prompt: requestData.prompt,
            model: requestData.model,
            size: requestData.size,
            num_images: requestData.num_images,
            has_image_file: !!requestData.image_file,
            image_file_length: requestData.image_file.length
        });
        
        const response = await fetch('http://localhost:3000/api/generate-image', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestData)
        });
        
        const data = await response.json();
        
        console.log('Response status:', response.status);
        console.log('Response data:', data);
        
        if (response.ok && data.success) {
            console.log('✅ Image edit API test successful!');
            console.log('Model used:', data.metadata.model_used);
            console.log('Generation time:', data.metadata.generation_time + 'ms');
        } else {
            console.log('❌ Image edit API test failed');
            console.log('Error:', data.error || 'Unknown error');
        }
        
    } catch (error) {
        console.error('❌ Test failed with error:', error.message);
    }
}

// 运行测试
testImageEditAPI();
