<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Image Edit</title>
</head>
<body>
    <h1>Test Image Edit API</h1>
    
    <div>
        <h2>Upload Image</h2>
        <input type="file" id="imageInput" accept="image/*">
        <br><br>
        <img id="preview" style="max-width: 300px; display: none;">
    </div>
    
    <div>
        <h2>Edit Prompt</h2>
        <input type="text" id="promptInput" placeholder="Enter edit instruction" value="convert to cartoon style">
        <br><br>
        <button onclick="testImageEdit()">Test Image Edit</button>
    </div>
    
    <div id="result"></div>
    
    <script>
        const imageInput = document.getElementById('imageInput');
        const preview = document.getElementById('preview');
        const promptInput = document.getElementById('promptInput');
        const result = document.getElementById('result');
        
        let selectedFile = null;
        
        imageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                selectedFile = file;
                const url = URL.createObjectURL(file);
                preview.src = url;
                preview.style.display = 'block';
                console.log('File selected:', file.name, file.size);
            }
        });
        
        async function testImageEdit() {
            if (!selectedFile) {
                alert('Please select an image first');
                return;
            }
            
            const prompt = promptInput.value;
            if (!prompt) {
                alert('Please enter a prompt');
                return;
            }
            
            console.log('Starting image edit test...');
            result.innerHTML = 'Processing...';
            
            try {
                // Convert file to base64
                const reader = new FileReader();
                const base64Promise = new Promise((resolve, reject) => {
                    reader.onload = () => resolve(reader.result);
                    reader.onerror = reject;
                    reader.readAsDataURL(selectedFile);
                });
                
                const base64Data = await base64Promise;
                console.log('Image converted to base64, length:', base64Data.length);
                
                // Call API
                const response = await fetch('/api/generate-image', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: prompt,
                        model: 'flux-schnell',
                        size: 'landscape',
                        num_images: 1,
                        image_file: base64Data
                    })
                });
                
                const data = await response.json();
                console.log('API response:', data);
                
                if (response.ok && data.success) {
                    result.innerHTML = `
                        <h3>Success!</h3>
                        <p>Model used: ${data.metadata.model_used}</p>
                        <p>Generation time: ${data.metadata.generation_time}ms</p>
                        <img src="${data.images[0].url}" style="max-width: 400px;">
                    `;
                } else {
                    result.innerHTML = `<h3>Error:</h3><p>${data.error || 'Unknown error'}</p>`;
                }
            } catch (error) {
                console.error('Test failed:', error);
                result.innerHTML = `<h3>Test failed:</h3><p>${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
