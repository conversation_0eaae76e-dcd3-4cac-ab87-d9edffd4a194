/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-image/route";
exports.ids = ["app/api/generate-image/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_liupeng_cursor_Flux_flux_chat_app_src_app_api_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-image/route.ts */ \"(rsc)/./src/app/api/generate-image/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-image/route\",\n        pathname: \"/api/generate-image\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-image/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/api/generate-image/route.ts\",\n    nextConfigOutput,\n    userland: _Users_liupeng_cursor_Flux_flux_chat_app_src_app_api_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-image/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/generate-image/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_imageGeneration__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/imageGeneration */ \"(rsc)/./src/lib/imageGeneration.ts\");\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { prompt, model = 'flux-schnell', size = 'landscape', num_images = 1, style_preset, guidance_scale, num_inference_steps, seed, negative_prompt, batch_mode = false, // 图像编辑参数\n        image_file, image_url, strength = 0.95 // 编辑强度\n         } = body;\n        // 验证必需参数\n        if (!prompt || typeof prompt !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Prompt is required and must be a string'\n            }, {\n                status: 400\n            });\n        }\n        // 处理图片上传（如果有）\n        let finalImageUrl = image_url;\n        if (image_file && !image_url) {\n            try {\n                // 将base64转换为Blob对象（Node.js兼容）\n                const base64Data = image_file.split(',')[1]; // 移除data:image/...;base64,前缀\n                const mimeType = image_file.split(',')[0].split(':')[1].split(';')[0];\n                const buffer = Buffer.from(base64Data, 'base64');\n                // 创建一个类似File的对象，兼容fal.ai的上传接口\n                const fileObject = {\n                    stream: ()=>new ReadableStream({\n                            start (controller) {\n                                controller.enqueue(new Uint8Array(buffer));\n                                controller.close();\n                            }\n                        }),\n                    arrayBuffer: ()=>Promise.resolve(buffer.buffer),\n                    name: 'uploaded-image',\n                    type: mimeType,\n                    size: buffer.length\n                };\n                // 上传到fal.ai存储\n                finalImageUrl = await (0,_lib_imageGeneration__WEBPACK_IMPORTED_MODULE_1__.uploadImageToFal)(fileObject);\n                console.log('Image uploaded to fal.ai:', finalImageUrl);\n            } catch (uploadError) {\n                console.error('Failed to upload image:', uploadError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Failed to upload image'\n                }, {\n                    status: 500\n                });\n            }\n        }\n        // 构建生成参数\n        let params = {\n            prompt,\n            model,\n            size,\n            num_images: Math.min(num_images, 4),\n            guidance_scale,\n            num_inference_steps,\n            seed,\n            negative_prompt,\n            // 图像编辑参数\n            image_url: finalImageUrl,\n            strength\n        };\n        // 检查API密钥，如果没有则返回演示结果\n        const hasValidFalKey = process.env.FAL_KEY && process.env.FAL_KEY !== 'your_fal_api_key_here' || process.env.FAL_API_KEY && process.env.FAL_API_KEY !== 'your_fal_api_key_here';\n        if (!hasValidFalKey) {\n            // 演示模式 - 返回模拟的图像生成结果\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                demo_mode: true,\n                images: [\n                    {\n                        url: 'https://picsum.photos/1024/768?random=' + Date.now(),\n                        width: 1024,\n                        height: 768,\n                        content_type: 'image/jpeg'\n                    }\n                ],\n                metadata: {\n                    model_used: params.model,\n                    generation_time: 3000 + Math.random() * 2000,\n                    seed: Math.floor(Math.random() * 1000000),\n                    prompt_used: prompt,\n                    parameters: params,\n                    demo_notice: 'This is a demo image. Configure FAL_API_KEY for real image generation.'\n                }\n            });\n        }\n        // 应用风格预设\n        if (style_preset && style_preset in _lib_imageGeneration__WEBPACK_IMPORTED_MODULE_1__.STYLE_PRESETS) {\n            params = (0,_lib_imageGeneration__WEBPACK_IMPORTED_MODULE_1__.applyStylePreset)(params, style_preset);\n        }\n        console.log('Image generation request:', {\n            prompt: prompt.substring(0, 100) + '...',\n            model,\n            size,\n            num_images: params.num_images,\n            has_image_file: !!image_file,\n            has_image_url: !!finalImageUrl,\n            is_image_editing: !!finalImageUrl\n        });\n        // 生成图像\n        const result = batch_mode && num_images > 1 ? await (0,_lib_imageGeneration__WEBPACK_IMPORTED_MODULE_1__.generateMultipleImages)(params, num_images) : await (0,_lib_imageGeneration__WEBPACK_IMPORTED_MODULE_1__.generateImage)(params);\n        if (result.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                images: result.images,\n                metadata: {\n                    model_used: result.model_used,\n                    generation_time: result.generation_time,\n                    seed: result.seed,\n                    prompt_used: prompt,\n                    parameters: params\n                }\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: result.error,\n                success: false,\n                model_used: result.model_used\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('Image generation API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET方法用于获取支持的模型和配置\nasync function GET() {\n    try {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            models: {\n                'flux-schnell': {\n                    name: 'FLUX Schnell',\n                    description: '快速生成，4步即可完成',\n                    speed: 'fast',\n                    quality: 'good'\n                },\n                'flux-dev': {\n                    name: 'FLUX Dev',\n                    description: '开发版本，平衡速度和质量',\n                    speed: 'medium',\n                    quality: 'high'\n                },\n                'flux-pro': {\n                    name: 'FLUX Pro',\n                    description: '专业版本，最高质量',\n                    speed: 'slow',\n                    quality: 'excellent'\n                },\n                'sd-xl': {\n                    name: 'Stable Diffusion XL',\n                    description: '经典的高质量模型',\n                    speed: 'medium',\n                    quality: 'high'\n                }\n            },\n            sizes: {\n                'square': '1024x1024',\n                'portrait': '768x1024',\n                'landscape': '1024x768',\n                'wide': '1344x768',\n                'tall': '768x1344'\n            },\n            style_presets: Object.keys(_lib_imageGeneration__WEBPACK_IMPORTED_MODULE_1__.STYLE_PRESETS),\n            limits: {\n                max_images_per_request: 4,\n                max_prompt_length: 1000\n            }\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to get configuration'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-image/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/imageGeneration.ts":
/*!************************************!*\
  !*** ./src/lib/imageGeneration.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IMAGE_MODELS: () => (/* binding */ IMAGE_MODELS),\n/* harmony export */   IMAGE_SIZES: () => (/* binding */ IMAGE_SIZES),\n/* harmony export */   IMAGE_SIZE_PIXELS: () => (/* binding */ IMAGE_SIZE_PIXELS),\n/* harmony export */   STYLE_PRESETS: () => (/* binding */ STYLE_PRESETS),\n/* harmony export */   applyStylePreset: () => (/* binding */ applyStylePreset),\n/* harmony export */   checkServiceStatus: () => (/* binding */ checkServiceStatus),\n/* harmony export */   generateImage: () => (/* binding */ generateImage),\n/* harmony export */   generateMultipleImages: () => (/* binding */ generateMultipleImages),\n/* harmony export */   uploadImageToFal: () => (/* binding */ uploadImageToFal)\n/* harmony export */ });\n/* harmony import */ var _fal_ai_serverless_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @fal-ai/serverless-client */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/index.js\");\n\n// 配置fal.ai客户端\n_fal_ai_serverless_client__WEBPACK_IMPORTED_MODULE_0__.config({\n    credentials: process.env.FAL_KEY || process.env.FAL_API_KEY\n});\n// 支持的图像生成模型\nconst IMAGE_MODELS = {\n    // FLUX模型 - 高质量图像生成\n    'flux-pro': 'fal-ai/flux-pro',\n    'flux-dev': 'fal-ai/flux/dev',\n    'flux-schnell': 'fal-ai/flux/schnell',\n    // FLUX Image-to-Image模型\n    'flux-dev-img2img': 'fal-ai/flux/dev/image-to-image',\n    // Stable Diffusion模型\n    'sd-xl': 'fal-ai/stable-diffusion-xl',\n    'sd-3': 'fal-ai/stable-diffusion-v3-medium',\n    // 其他专业模型\n    'playground': 'fal-ai/playground-v2-5',\n    'kandinsky': 'fal-ai/kandinsky-3'\n};\n// 图像尺寸预设 - 映射到fal.ai的格式\nconst IMAGE_SIZES = {\n    'square': 'square_hd',\n    'portrait': 'portrait_4_3',\n    'landscape': 'landscape_4_3',\n    'wide': 'landscape_16_9',\n    'tall': 'portrait_16_9'\n};\n// 尺寸对应的实际像素值（用于显示）\nconst IMAGE_SIZE_PIXELS = {\n    'square_hd': {\n        width: 1024,\n        height: 1024\n    },\n    'square': {\n        width: 512,\n        height: 512\n    },\n    'portrait_4_3': {\n        width: 768,\n        height: 1024\n    },\n    'portrait_16_9': {\n        width: 576,\n        height: 1024\n    },\n    'landscape_4_3': {\n        width: 1024,\n        height: 768\n    },\n    'landscape_16_9': {\n        width: 1024,\n        height: 576\n    }\n};\n// 上传图片到fal.ai存储\nasync function uploadImageToFal(imageFile) {\n    try {\n        console.log('Uploading image to fal.ai storage...');\n        const url = await _fal_ai_serverless_client__WEBPACK_IMPORTED_MODULE_0__.storage.upload(imageFile);\n        console.log('Image uploaded successfully:', url);\n        return url;\n    } catch (error) {\n        console.error('Failed to upload image:', error);\n        throw new Error('Failed to upload image to fal.ai storage');\n    }\n}\n// 主要图像生成函数\nasync function generateImage(params) {\n    // 检查API密钥\n    const hasValidFalKey = process.env.FAL_KEY && process.env.FAL_KEY !== 'your_fal_api_key_here' || process.env.FAL_API_KEY && process.env.FAL_API_KEY !== 'your_fal_api_key_here';\n    if (!hasValidFalKey) {\n        return {\n            success: false,\n            error: 'FAL API key not configured',\n            model_used: params.model || 'flux-schnell'\n        };\n    }\n    try {\n        // 判断是否为图像编辑模式\n        const isImageEditing = params.image_url && params.image_url.length > 0;\n        // 如果是图像编辑，强制使用flux-dev-img2img模型\n        const model = isImageEditing ? 'flux-dev-img2img' : params.model || 'flux-schnell';\n        const modelEndpoint = IMAGE_MODELS[model];\n        const imageSize = IMAGE_SIZES[params.size || 'landscape'];\n        console.log(`Generating image with model: ${model} (${modelEndpoint})`);\n        console.log('Is image editing mode:', isImageEditing);\n        // 构建请求参数 - 根据不同模型使用不同的参数格式\n        let requestParams = {\n            prompt: params.prompt\n        };\n        // 图像编辑模式 (Image-to-Image)\n        if (isImageEditing && model === 'flux-dev-img2img') {\n            requestParams = {\n                image_url: params.image_url,\n                prompt: params.prompt,\n                strength: params.strength || 0.95,\n                num_inference_steps: params.num_inference_steps || 40,\n                guidance_scale: params.guidance_scale || 3.5,\n                num_images: params.num_images || 1,\n                enable_safety_checker: true,\n                ...params.seed && {\n                    seed: params.seed\n                }\n            };\n        } else if (model.startsWith('flux') && !isImageEditing) {\n            requestParams = {\n                prompt: params.prompt,\n                image_size: imageSize,\n                num_images: params.num_images || 1,\n                num_inference_steps: getDefaultSteps(model),\n                enable_safety_checker: true,\n                ...params.seed && {\n                    seed: params.seed\n                }\n            };\n        } else {\n            const pixelSize = IMAGE_SIZE_PIXELS[imageSize] || IMAGE_SIZE_PIXELS['landscape_4_3'];\n            requestParams = {\n                prompt: params.prompt,\n                width: pixelSize.width,\n                height: pixelSize.height,\n                num_images: params.num_images || 1,\n                guidance_scale: params.guidance_scale || 7.5,\n                num_inference_steps: params.num_inference_steps || getDefaultSteps(model),\n                ...params.negative_prompt && {\n                    negative_prompt: params.negative_prompt\n                },\n                ...params.seed && {\n                    seed: params.seed\n                }\n            };\n        }\n        console.log(`Generating image with model: ${model} (${modelEndpoint})`);\n        console.log('Request parameters:', JSON.stringify(requestParams, null, 2));\n        const startTime = Date.now();\n        // 调用fal.ai API\n        const result = await _fal_ai_serverless_client__WEBPACK_IMPORTED_MODULE_0__.subscribe(modelEndpoint, {\n            input: requestParams,\n            logs: true,\n            onQueueUpdate: (update)=>{\n                console.log('Queue update:', update);\n            }\n        });\n        const generationTime = Date.now() - startTime;\n        // fal.ai返回的数据直接在result中，不在result.data中\n        if (result && result.images && result.images.length > 0) {\n            const pixelSize = model.startsWith('flux') ? IMAGE_SIZE_PIXELS[imageSize] || IMAGE_SIZE_PIXELS['landscape_4_3'] : IMAGE_SIZE_PIXELS[imageSize] || IMAGE_SIZE_PIXELS['landscape_4_3'];\n            return {\n                success: true,\n                images: result.images.map((img)=>({\n                        url: img.url,\n                        width: img.width || pixelSize.width,\n                        height: img.height || pixelSize.height,\n                        content_type: img.content_type || 'image/jpeg'\n                    })),\n                model_used: model,\n                generation_time: generationTime,\n                seed: result.seed\n            };\n        } else {\n            throw new Error('No images generated');\n        }\n    } catch (error) {\n        console.error('Image generation error:', error);\n        // 提取更详细的错误信息\n        let errorMessage = 'Unknown error occurred';\n        if (error instanceof Error) {\n            errorMessage = error.message;\n            // 如果是fal.ai的错误，尝试提取更多信息\n            if ('body' in error && error.body) {\n                console.error('Error body:', error.body);\n                errorMessage += ` (Status: ${error.status})`;\n            }\n        }\n        return {\n            success: false,\n            error: errorMessage,\n            model_used: params.model || 'flux-schnell'\n        };\n    }\n}\n// 获取模型默认步数\nfunction getDefaultSteps(model) {\n    const stepMap = {\n        'flux-pro': 50,\n        'flux-dev': 50,\n        'flux-schnell': 4,\n        'flux-dev-img2img': 40,\n        'sd-xl': 30,\n        'sd-3': 28,\n        'playground': 50,\n        'kandinsky': 100\n    };\n    return stepMap[model] || 30;\n}\n// 批量图像生成\nasync function generateMultipleImages(params, count = 4) {\n    try {\n        const batchParams = {\n            ...params,\n            num_images: Math.min(count, 4)\n        };\n        return await generateImage(batchParams);\n    } catch (error) {\n        console.error('Batch image generation error:', error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : 'Batch generation failed',\n            model_used: params.model || 'flux-schnell'\n        };\n    }\n}\n// 图像风格预设\nconst STYLE_PRESETS = {\n    'photorealistic': {\n        negative_prompt: 'cartoon, anime, painting, drawing, sketch, low quality, blurry',\n        guidance_scale: 7.5\n    },\n    'artistic': {\n        negative_prompt: 'low quality, blurry, distorted',\n        guidance_scale: 8.0\n    },\n    'anime': {\n        negative_prompt: 'realistic, photograph, low quality, blurry',\n        guidance_scale: 7.0\n    },\n    'cartoon': {\n        negative_prompt: 'realistic, photograph, dark, scary, low quality',\n        guidance_scale: 7.0\n    },\n    'cinematic': {\n        negative_prompt: 'low quality, amateur, snapshot, casual',\n        guidance_scale: 8.5\n    }\n};\n// 应用风格预设\nfunction applyStylePreset(params, style) {\n    const preset = STYLE_PRESETS[style];\n    return {\n        ...params,\n        negative_prompt: preset.negative_prompt,\n        guidance_scale: preset.guidance_scale\n    };\n}\n// 检查fal.ai服务状态\nasync function checkServiceStatus() {\n    try {\n        // 尝试调用一个简单的模型来检查服务状态\n        await _fal_ai_serverless_client__WEBPACK_IMPORTED_MODULE_0__.subscribe('fal-ai/flux/schnell', {\n            input: {\n                prompt: 'test',\n                image_size: 'square',\n                num_images: 1,\n                num_inference_steps: 1\n            },\n            timeout: 5000\n        });\n        return true;\n    } catch (error) {\n        console.error('Service status check failed:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/imageGeneration.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@msgpack","vendor-chunks/@fal-ai","vendor-chunks/eventsource-parser","vendor-chunks/robot3"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();