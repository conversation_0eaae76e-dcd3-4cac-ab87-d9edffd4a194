"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/page.tsx":
/*!*******************************!*\
  !*** ./src/app/chat/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_PhotoIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=PhotoIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PhotoIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=PhotoIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _components_Chat_ImageDisplay__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Chat/ImageDisplay */ \"(app-pages-browser)/./src/components/Chat/ImageDisplay.tsx\");\n/* harmony import */ var _components_Chat_ImageGenerationPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Chat/ImageGenerationPanel */ \"(app-pages-browser)/./src/components/Chat/ImageGenerationPanel.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ChatPage() {\n    _s();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGeneratingImage, setIsGeneratingImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [optimizedPrompt, setOptimizedPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const sendMessage = async ()=>{\n        if (!message.trim() && !selectedImage) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: 'user',\n            content: message,\n            image: selectedImage || undefined\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setMessage('');\n        setSelectedImage(null);\n        setIsLoading(true);\n        try {\n            var _data_optimization;\n            // 准备请求数据\n            const requestData = {\n                message: userMessage.content\n            };\n            // 如果有图片，转换为base64\n            if (selectedImage) {\n                const base64 = await fileToBase64(selectedImage.file);\n                requestData.image = {\n                    data: base64,\n                    mimeType: selectedImage.file.type\n                };\n            }\n            // 直接生成优化提示词\n            const response = await fetch('/api/optimize-prompt', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    requirement: userMessage.content,\n                    conversationHistory: [],\n                    useAI: true,\n                    image: requestData.image\n                })\n            });\n            const data = await response.json();\n            if (response.ok && ((_data_optimization = data.optimization) === null || _data_optimization === void 0 ? void 0 : _data_optimization.optimizedPrompt)) {\n                setOptimizedPrompt(data.optimization.optimizedPrompt);\n                const assistantMessage = {\n                    id: (Date.now() + 1).toString(),\n                    role: 'assistant',\n                    content: \"已为您生成优化的提示词：\\n\\n\".concat(data.optimization.optimizedPrompt)\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        assistantMessage\n                    ]);\n            } else {\n                setMessages((prev)=>[\n                        ...prev,\n                        {\n                            id: (Date.now() + 1).toString(),\n                            role: 'assistant',\n                            content: '抱歉，生成提示词时发生了错误。请稍后重试。'\n                        }\n                    ]);\n            }\n        } catch (error) {\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        id: (Date.now() + 1).toString(),\n                        role: 'assistant',\n                        content: '网络错误，请检查连接。'\n                    }\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 处理图像生成\n    const handleImageGeneration = async (config, prompt, imageFile)=>{\n        setIsGeneratingImage(true);\n        try {\n            // 准备请求数据\n            const requestData = {\n                prompt,\n                ...config\n            };\n            // 如果有图片文件，转换为base64并添加到请求中\n            if (imageFile) {\n                try {\n                    const reader = new FileReader();\n                    const base64Promise = new Promise((resolve, reject)=>{\n                        reader.onload = ()=>resolve(reader.result);\n                        reader.onerror = reject;\n                        reader.readAsDataURL(imageFile);\n                    });\n                    const base64Data = await base64Promise;\n                    requestData.image_file = base64Data;\n                    console.log('Adding image to generation request');\n                } catch (error) {\n                    console.error('Failed to convert image to base64:', error);\n                }\n            }\n            const response = await fetch('/api/generate-image', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestData)\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                // 添加图像消息\n                const imageMessage = {\n                    id: Date.now().toString(),\n                    role: 'assistant',\n                    content: \"已为您生成图像！使用了 \".concat(data.metadata.model_used, \" 模型。\"),\n                    images: data.images,\n                    metadata: data.metadata\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        imageMessage\n                    ]);\n            } else {\n                // 处理错误\n                const errorMessage = {\n                    id: Date.now().toString(),\n                    role: 'assistant',\n                    content: data.demo_mode ? '图像生成功能需要配置 FAL API 密钥。请查看配置说明。' : \"图像生成失败: \".concat(data.error || '未知错误')\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n            }\n        } catch (error) {\n            const errorMessage = {\n                id: Date.now().toString(),\n                role: 'assistant',\n                content: '图像生成请求失败，请检查网络连接。'\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsGeneratingImage(false);\n        }\n    };\n    // 处理图片上传\n    const handleImageUpload = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file && file.type.startsWith('image/')) {\n            const url = URL.createObjectURL(file);\n            setSelectedImage({\n                url,\n                file\n            });\n        }\n    };\n    // 移除图片\n    const handleImageRemove = ()=>{\n        if (selectedImage) {\n            URL.revokeObjectURL(selectedImage.url);\n            setSelectedImage(null);\n        }\n        if (fileInputRef.current) {\n            fileInputRef.current.value = '';\n        }\n    };\n    // 点击图片按钮\n    const handleImageButtonClick = ()=>{\n        var _fileInputRef_current;\n        (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n    };\n    // 将文件转换为base64\n    const fileToBase64 = (file)=>{\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.readAsDataURL(file);\n            reader.onload = ()=>{\n                const result = reader.result;\n                // 移除data:image/jpeg;base64,前缀，只保留base64数据\n                const base64 = result.split(',')[1];\n                resolve(base64);\n            };\n            reader.onerror = (error)=>reject(error);\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: \"AI图像提示词生成器\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: \"基于DeepSeek，直接生成专业的图像提示词\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4\",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-gray-500 mt-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg font-medium\",\n                            children: \"AI图像提示词生成器\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm mt-2\",\n                            children: \"描述您的图像需求，我将直接为您生成专业的提示词\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 max-w-4xl mx-auto\",\n                    children: [\n                        messages.map((msg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex \".concat(msg.role === 'user' ? 'justify-end' : 'justify-start'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-[80%] \".concat(msg.role === 'user' ? '' : 'w-full'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"rounded-lg px-4 py-2 \".concat(msg.role === 'user' ? 'bg-blue-500 text-white' : 'bg-white text-gray-900 border border-gray-200'),\n                                            children: [\n                                                msg.image && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: msg.image.url,\n                                                        alt: \"用户上传的图片\",\n                                                        className: \"max-w-full max-h-64 rounded-lg border border-gray-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 23\n                                                }, this),\n                                                msg.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"whitespace-pre-wrap\",\n                                                    children: msg.content\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 19\n                                        }, this),\n                                        msg.images && msg.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Chat_ImageDisplay__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                images: msg.images,\n                                                metadata: msg.metadata,\n                                                onRegenerate: (seed)=>{\n                                                    var _msg_metadata;\n                                                    if ((_msg_metadata = msg.metadata) === null || _msg_metadata === void 0 ? void 0 : _msg_metadata.prompt_used) {\n                                                        var _msg_metadata_parameters, _msg_metadata_parameters1;\n                                                        handleImageGeneration({\n                                                            model: ((_msg_metadata_parameters = msg.metadata.parameters) === null || _msg_metadata_parameters === void 0 ? void 0 : _msg_metadata_parameters.model) || 'flux-schnell',\n                                                            size: ((_msg_metadata_parameters1 = msg.metadata.parameters) === null || _msg_metadata_parameters1 === void 0 ? void 0 : _msg_metadata_parameters1.size) || 'landscape',\n                                                            num_images: 1,\n                                                            seed\n                                                        }, msg.metadata.prompt_used);\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 17\n                                }, this)\n                            }, msg.id, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 15\n                            }, this)),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white border border-gray-200 rounded-lg px-4 py-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500\",\n                                            children: \"AI正在思考...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-t border-gray-200 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto space-y-3\",\n                    children: [\n                        selectedImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative inline-block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: selectedImage.url,\n                                        alt: \"上传的图片\",\n                                        className: \"max-w-xs max-h-32 rounded-lg border border-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleImageRemove,\n                                        className: \"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PhotoIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 13\n                        }, this),\n                        optimizedPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Chat_ImageGenerationPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                onGenerate: handleImageGeneration,\n                                isGenerating: isGeneratingImage,\n                                optimizedPrompt: optimizedPrompt\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleImageButtonClick,\n                                    className: \"flex-shrink-0 p-2 text-gray-600 hover:text-blue-600 transition-colors\",\n                                    title: \"上传图片\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PhotoIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: fileInputRef,\n                                    type: \"file\",\n                                    accept: \"image/*\",\n                                    onChange: handleImageUpload,\n                                    className: \"hidden\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: message,\n                                    onChange: (e)=>setMessage(e.target.value),\n                                    onKeyPress: (e)=>e.key === 'Enter' && sendMessage(),\n                                    placeholder: \"描述您的图像需求，我将生成专业提示词...\",\n                                    disabled: isLoading || isGeneratingImage,\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: sendMessage,\n                                    disabled: !message.trim() && !selectedImage || isLoading || isGeneratingImage,\n                                    className: \"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors\",\n                                    children: isLoading ? '发送中...' : '发送'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2 justify-center\",\n                            children: [\n                                '可爱的小猫',\n                                '未来城市',\n                                '油画风景',\n                                '卡通人物',\n                                '抽象艺术'\n                            ].map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setMessage(prompt),\n                                    disabled: isLoading || isGeneratingImage,\n                                    className: \"px-3 py-1 text-sm bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                    children: prompt\n                                }, prompt, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"+NPKr41MHxfUWE2KhgJBJpvqkNw=\");\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/page.tsx\n"));

/***/ })

});